<?php
use app\common\models\PaymentType;
use app\modules\backend\models\WorkerFinances;
use yii\helpers\Html;
use app\assets\Select2Asset;

Select2Asset::register($this);
?>

<style>
.payment-methods-container {
    padding: 8px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.payment-methods-container .form-check {
    margin-bottom: 0;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    min-width: 120px;
    position: relative;
}

.payment-methods-container .form-check:hover {
    border-color: #007bff;
    box-shadow: 0 1px 3px rgba(0,123,255,0.1);
}

.payment-methods-container .form-check-input {
    margin: 0;
    transform: scale(1.1);
    flex-shrink: 0;
    position: absolute;
    right: 10px;
}

.payment-methods-container .form-check-label {
    font-weight: 500;
    font-size: 14px;
    color: #495057;
    cursor: pointer;
    margin: 0;
    white-space: nowrap;
    text-align: left;
    padding-right: 24px;
}

.payment-methods-container .form-check-input:disabled + .form-check-label {
    opacity: 0.5;
    cursor: not-allowed;
}

.payment-methods-container .form-check-input:checked + .form-check-label {
    color: #007bff;
    font-weight: 600;
}

/* Стили для основных чекбоксов типов платежей */
.payment-type-row .form-check {
    padding: 12px 15px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    min-width: 150px;
    position: relative;
}

.payment-type-row .form-check:hover {
    background: #e3f2fd;
    border-color: #2196f3;
}

.payment-type-row .form-check-input {
    margin: 0;
    transform: scale(1.2);
    flex-shrink: 0;
    position: absolute;
    right: 10px;
}

.payment-type-row .form-check-label {
    font-weight: 600;
    font-size: 15px;
    color: #343a40;
    margin: 0;
    white-space: nowrap;
    text-align: left;
    padding-right: 24px;
}

.payment-type-row .form-check-input:checked + .form-check-label {
    color:hsl(207, 89.70%, 54.10%);
}

/* Выравнивание контента в ячейках таблицы */
.payment-type-row td {
    vertical-align: middle;
}

.amount-column {
    vertical-align: middle;
}

/* Стили для полей ввода чисел */
input[inputmode="numeric"] {
    text-align: right;
}

/* Стили для секции погашения долга */
#debt-payment-section .card {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background: white;
}

#debt-payment-section .card-header {
    background:rgb(160, 190, 239);
    border-bottom: 1px solid #dee2e6;
    padding: 12px 15px;
}

#debt-payment-section .form-check {
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    position: relative;
    cursor: pointer;
}

#debt-payment-section .form-check:hover {
    background: #f8f9fa;
    border-radius: 4px;
    padding: 8px 12px;
    margin: -8px -12px;
}

#debt-payment-section .form-check-input {
    margin: 0;
    transform: scale(1.2);
    flex-shrink: 0;
    position: absolute;
    right: 10px;
}

#debt-payment-section .form-check-label {
    font-weight: 350;
    font-size: 15px;
    color:rgb(2, 11, 20);
    margin: 0;
    cursor: pointer;
    padding-right: 30px;
    width: 100%;
}

#debt-payment-section .form-check-input:checked + .form-check-label {
    color: #dc3545;
}

#debt-payment-section .card-body {
    background: white;
    padding: 20px;
}
</style>

<div class="worker-payment-form">
    <form id="worker-payment-create-form">
        
        <div class="row mb-3">
            <div class="col-md-8">
                <label for="worker_id"><?= Yii::t('app', 'worker') ?></label>
                <select id="worker_id" name="worker_id" class="form-control select2" required>
                    <option value=""><?= Yii::t('app', 'select_worker') ?></option>
                    <?php foreach ($workers as $worker): ?>
                        <option value="<?= $worker['id'] ?>"><?= Html::encode($worker['full_name']) ?></option>
                    <?php endforeach; ?>
                </select>
                <div class="error-container" id="worker_id-error"></div>
            </div>
            <div class="col-md-4">
                <label for="month"><?= Yii::t('app', 'month') ?></label>
                <input type="month" id="month" name="month" class="form-control" value="<?= date('Y-m') ?>" required>
                <div class="error-container" id="month-error"></div>
            </div>
        </div>

        <!-- Worker Info Panel -->
        <div id="worker-info" class="alert alert-info mt-3" style="display: none;">
            <div class="row">
                <div class="col-md-4">
                    <strong><?= Yii::t('app', 'salary') ?>:</strong> <span id="worker-salary">0</span>
                </div>
                <div class="col-md-4">
                    <strong><?= Yii::t('app', 'total_paid') ?>:</strong> <span id="worker-total-paid">0</span>
                </div>
                <div class="col-md-4" id="remaining-salary-info" style="display: none;">
                    <strong><?= Yii::t('app', 'remaining_salary') ?>:</strong> <span id="worker-remaining-salary">0</span>
                </div>
            </div>
        </div>

        <!-- Payment Types Selection -->
        <div class="form-group">
            <label><?= Yii::t('app', 'payment_types') ?></label>
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th width="40%"><?= Yii::t('app', 'payment_type') ?></th>
                            <th width="30%"><?= Yii::t('app', 'payment_method') ?></th>
                            <th width="30%"><?= Yii::t('app', 'amount') ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($paymentTypes as $typeId => $typeName): ?>
                            <?php if ($typeId != WorkerFinances::TYPE_DEBT_PAYMENT): // Исключаем долг из основной таблицы ?>
                                <tr class="payment-type-row" data-type="<?= $typeId ?>">
                                    <td>
                                        <div class="form-check">
                                            <input class="form-check-input payment-type-checkbox" type="checkbox" 
                                                   id="payment_type_<?= $typeId ?>" value="<?= $typeId ?>">
                                            <label class="form-check-label" for="payment_type_<?= $typeId ?>">
                                                <?= $typeName ?>
                                            </label>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="payment-methods-container">
                                            <div class="form-check">
                                                <input class="form-check-input payment-method-checkbox" type="checkbox" 
                                                       id="cash_<?= $typeId ?>" value="<?= PaymentType::CASH ?>"
                                                       name="payment_types[<?= $typeId ?>][methods][]" disabled>
                                                <label class="form-check-label" for="cash_<?= $typeId ?>">
                                                    <?= Yii::t('app', 'cash') ?>
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input payment-method-checkbox" type="checkbox" 
                                                       id="card_<?= $typeId ?>" value="<?= PaymentType::PAYMENT_CARD ?>"
                                                       name="payment_types[<?= $typeId ?>][methods][]" disabled>
                                                <label class="form-check-label" for="card_<?= $typeId ?>">
                                                    <?= Yii::t('app', 'payment_card') ?>
                                                </label>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="amount-column">
                                        <input type="text" 
                                               class="form-control amount-input" 
                                               name="payment_types[<?= $typeId ?>][amount]" 
                                               placeholder="<?= Yii::t('app', 'amount') ?>" 
                                               inputmode="numeric"
                                               pattern="[0-9\s]*"
                                               disabled>
                                        <div class="dynamic-amounts" style="display: none;">
                                            <!-- Dynamic inputs will be added here -->
                                        </div>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Debt Payment Section -->
        <div id="debt-payment-section" class="form-group" style="display: none;">
                <div class="card-header">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="debt-payment-checkbox">
                        <label class="form-check-label" for="debt-payment-checkbox">
                            <strong><?= $paymentTypes[WorkerFinances::TYPE_DEBT_PAYMENT] ?? 'Погашение долга' ?></strong>
                        </label>
                    </div>
                </div>
                <div class="card-body" id="debt-payment-details" style="display: none;">
                    <div class="row">
                        <div class="col-md-12">
                            <label><?= Yii::t('app', 'amount') ?></label>
                            <input type="text" class="form-control payment-amount" id="debt-payment-amount"
                                   name="payment_types[<?= WorkerFinances::TYPE_DEBT_PAYMENT ?>][amount]" 
                                   inputmode="numeric" pattern="[0-9\s]*" disabled>
                            <small class="text-muted"><?= Yii::t('app', 'debt_amount') ?>: <span id="worker-debt-amount">0</span></small>
                        </div>
                    </div>
                </div>
        </div>


    </form>
</div>

<script>
$(document).ready(function() {
    // Делаем весь контейнер кликабельным для чекбокса погашения долга
    $('#debt-payment-section .form-check').on('click', function(e) {
        // Проверяем, что клик был не на самом чекбоксе и не на лейбле (т.к. лейбл уже связан с чекбоксом)
        if (!$(e.target).is('input[type="checkbox"], label')) {
            var checkbox = $(this).find('input[type="checkbox"]');
            checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
        }
    });
    
    // Делаем весь контейнер кликабельным для чекбоксов типов платежей
    $('.payment-type-row .form-check').on('click', function(e) {
        // Проверяем, что клик был не на самом чекбоксе и не на лейбле (т.к. лейбл уже связан с чекбоксом)
        if (!$(e.target).is('input[type="checkbox"], label')) {
            var checkbox = $(this).find('input[type="checkbox"]');
            checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
        }
    });
    
    // Делаем весь контейнер кликабельным для чекбоксов методов оплаты
    $('.payment-methods-container .form-check').on('click', function(e) {
        // Проверяем, что клик был не на самом чекбоксе и не на лейбле (т.к. лейбл уже связан с чекбоксом)
        if (!$(e.target).is('input[type="checkbox"], label')) {
            var checkbox = $(this).find('input[type="checkbox"]');
            var row = $(this).closest('tr');
            var typeCheckbox = row.find('.payment-type-checkbox');
            
            // Если тип платежа еще не выбран, выбираем его сначала
            if (!typeCheckbox.prop('checked')) {
                typeCheckbox.prop('checked', true).trigger('change');
                // После этого выбираем текущий метод оплаты
                checkbox.prop('checked', true).trigger('change');
            } else if (!checkbox.prop('disabled')) {
                // Если тип платежа уже выбран и чекбокс не отключен, переключаем его
                checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
            }
        }
    });
    
    // Payment type checkbox handler
    $('.payment-type-checkbox').on('change', function() {
        var row = $(this).closest('tr');
        var paymentMethodCheckboxes = row.find('.payment-method-checkbox');
        var amountInput = row.find('.amount-input');
        
        if ($(this).is(':checked')) {
            // Enable fields when checkbox is checked
            paymentMethodCheckboxes.prop('disabled', false);
            amountInput.prop('disabled', false);
            // At least one payment method should be required
            paymentMethodCheckboxes.each(function() {
                $(this).attr('data-required', 'true');
            });
            
            // Автоматически выбираем наличный метод оплаты (Cash), если ни один метод ещё не выбран
            if (!row.find('.payment-method-checkbox:checked').length) {
                var cashCheckbox = row.find('.payment-method-checkbox[value="<?= PaymentType::CASH ?>"]');
                if (cashCheckbox.length) {
                    cashCheckbox.prop('checked', true).trigger('change');
                }
            }
        } else {
            // Disable fields when checkbox is unchecked
            paymentMethodCheckboxes.prop('disabled', true);
            amountInput.prop('disabled', true);
            paymentMethodCheckboxes.prop('checked', false);
            paymentMethodCheckboxes.removeAttr('data-required');
            amountInput.val('');
            
            // Hide dynamic amounts
            row.find('.dynamic-amounts').hide().empty();
            row.find('.amount-input').show();
        }
        
        calculateTotal();
    });

    // Payment method checkbox handler
    $(document).on('change', '.payment-method-checkbox', function() {
         var row = $(this).closest('tr');
         var amountColumn = row.find('.amount-column');
         var defaultInput = amountColumn.find('.amount-input');
         var dynamicContainer = amountColumn.find('.dynamic-amounts');
         
         var selectedMethods = [];
         row.find('.payment-method-checkbox:checked').each(function() {
             selectedMethods.push($(this).val());
         });
         
         if (selectedMethods.length === 0) {
             defaultInput.show();
             dynamicContainer.hide().empty();
         } else if (selectedMethods.length === 1) {
             defaultInput.show();
             defaultInput.attr('name', 'payment_types[' + getTypeId(row) + '][amounts][' + selectedMethods[0] + ']');
             dynamicContainer.hide().empty();
         } else {
             defaultInput.hide();
             dynamicContainer.show().empty();
             
             selectedMethods.forEach(function(method) {
                 var methodName = method == '<?= PaymentType::CASH ?>' ? '<?= Yii::t('app', 'cash') ?>' : '<?= Yii::t('app', 'payment_card') ?>';
                 var input = $('<div class="mb-2"><label>' + '</label><input type="text" class="form-control payment-amount" name="payment_types[' + getTypeId(row) + '][amounts][' + method + ']" placeholder=' + methodName + ' inputmode="numeric" pattern="[0-9\\s]*"></div>');
                 dynamicContainer.append(input);
             });
         }
     });
     
     function getTypeId(row) {
         return row.find('input[type="checkbox"]').first().val();
     }

    // Debt payment checkbox handler
    $('#debt-payment-checkbox').on('change', function() {
        var details = $('#debt-payment-details');
        var paymentMethod = $('#debt-payment-method');
        var amountInput = $('#debt-payment-amount');
        
        if ($(this).is(':checked')) {
            details.show();
            paymentMethod.prop('disabled', false);
            amountInput.prop('disabled', false);
            paymentMethod.prop('required', true);
            amountInput.prop('required', true);
        } else {
            details.hide();
            paymentMethod.prop('disabled', true);
            amountInput.prop('disabled', true);
            paymentMethod.prop('required', false);
            amountInput.prop('required', false);
            paymentMethod.val('');
            amountInput.val('');
        }
        
        calculateTotal();
    });

    // Amount input handler
    $(document).on('input', '.payment-amount', function() {
        calculateTotal();
    });

    // Worker selection handler
    $('#worker_id').on('change', function() {
        var workerId = $(this).val();
        var month = $('#month').val();
        
        if (workerId && month) {
            loadWorkerInfo(workerId, month);
        } else {
            $('#worker-info').hide();
        }
    });

    // Month change handler
    $('#month').on('change', function() {
        var workerId = $('#worker_id').val();
        var month = $(this).val();
        
        if (workerId && month) {
            loadWorkerInfo(workerId, month);
        }
    });

    function loadWorkerInfo(workerId, month) {
        $.ajax({
            url: '/backend/worker-payment/get-worker-info',
            type: 'GET',
            data: {
                worker_id: workerId,
                month: month
            },
            success: function(response) {
                if (response.success) {
                    $('#worker-salary').text(formatNumber(response.salary));
                    
                    // Вычисляем общую сумму выплат только по зарплате и авансу (исключаем долги и другие выплаты)
                    var salaryPaid = response.payments[<?= WorkerFinances::TYPE_SALARY ?>] || 0;
                    var advancePaid = response.payments[<?= WorkerFinances::TYPE_ADVANCE ?>] || 0;
                    var totalSalaryAndAdvancePaid = salaryPaid + advancePaid;
                    
                    $('#worker-total-paid').text(formatNumber(totalSalaryAndAdvancePaid));
                    $('#worker-info').show();
                    
                    // Show/hide debt section based on debt amount
                    if (response.debt && response.debt > 0) {
                        $('#worker-debt-amount').text(formatNumber(response.debt));
                        $('#debt-payment-section').show();
                        
                        // Show remaining salary info (будет пересчитано позже)
                        $('#remaining-salary-info').show();
                    } else {
                        $('#debt-payment-section').hide();
                        $('#debt-payment-checkbox').prop('checked', false).trigger('change');
                        
                        // Показываем оставшуюся зарплату, если есть что показать
                        var remainingSalary = response.salary - totalSalaryAndAdvancePaid;
                        
                        if (remainingSalary > 0) {
                            $('#worker-remaining-salary').text(formatNumber(remainingSalary));
                            $('#remaining-salary-info').show();
                        } else {
                            $('#remaining-salary-info').hide();
                        }
                    }
                    
                    // Update payment amounts based on existing payments
                    updatePaymentAmounts(response.payments, response.salary);
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('Ошибка при загрузке информации о работнике');
            }
        });
    }

    function updatePaymentAmounts(payments, salary) {
        // Reset all amounts and uncheck all checkboxes
        $('.payment-type-checkbox').prop('checked', false).trigger('change');
        
        // Set salary amount if not fully paid
        var salaryPaid = payments[<?= WorkerFinances::TYPE_SALARY ?>] || 0;
        var advancePaid = payments[<?= WorkerFinances::TYPE_ADVANCE ?>] || 0;
        var totalSalaryAndAdvancePaid = salaryPaid + advancePaid;
        var remainingSalary = salary - totalSalaryAndAdvancePaid;
        
        if (remainingSalary > 0) {
            var salaryCheckbox = $('#payment_type_<?= WorkerFinances::TYPE_SALARY ?>');
            salaryCheckbox.prop('checked', true).trigger('change');
            
            // Set default payment method and amount
            var salaryRow = salaryCheckbox.closest('tr');
            salaryRow.find('.payment-method-checkbox[value="<?= PaymentType::CASH ?>"]').prop('checked', true).trigger('change'); // Default to cash
            salaryRow.find('.amount-input').val(formatNumberInput(remainingSalary.toString()));
        }
        
        calculateTotal();
    }

    function calculateTotal() {
        var total = 0;
        var hasSalaryOrAdvance = false;
        
        // Calculate total from table rows (все выплаты)
        $('.payment-amount:not(:disabled)').each(function() {
            var amount = getNumericValue($(this).val());
            total += amount;
            
            // Проверяем, есть ли выплата зарплаты или аванса
            var row = $(this).closest('tr');
            var typeId = row.data('type');
            if (typeId == <?= WorkerFinances::TYPE_SALARY ?> || typeId == <?= WorkerFinances::TYPE_ADVANCE ?>) {
                hasSalaryOrAdvance = true;
            }
        });
        
        // Subtract debt payment amount ONLY if salary or advance is being paid
        if (!$('#debt-payment-amount').prop('disabled') && hasSalaryOrAdvance) {
            var debtPaymentAmount = getNumericValue($('#debt-payment-amount').val());
            total -= debtPaymentAmount; // ВЫЧИТАЕМ долг только при выплате зарплаты/аванса
        }
        
        $('#total-amount').text(formatNumber(total));
        
        // Пересчитываем оставшуюся зарплату
        updateRemainingSalary();
    }

    function updateRemainingSalary() {
        // Получаем изначальную зарплату
        var originalSalary = parseFloat($('#worker-salary').text().replace(/[^\d.-]/g, '')) || 0;
        
        // Считаем сумму выплат по зарплате и авансу (только текущие выплаты в форме)
        var currentSalaryAndAdvancePayments = 0;
        $('.payment-amount:not(:disabled)').each(function() {
            var amount = getNumericValue($(this).val());
            var row = $(this).closest('tr');
            var typeId = row.data('type');
            
            // Учитываем только зарплату и аванс
            if (typeId == <?= WorkerFinances::TYPE_SALARY ?> || typeId == <?= WorkerFinances::TYPE_ADVANCE ?>) {
                currentSalaryAndAdvancePayments += amount;
            }
        });
        
        // Получаем уже выплаченную сумму (только зарплата и аванс, без текущих выплат)
        var originalTotalPaid = parseFloat($('#worker-total-paid').text().replace(/[^\d.-]/g, '')) || 0;
        
        // Общая выплаченная сумма = уже выплаченное + текущие выплаты зарплаты/аванса
        var totalPaidIncludingCurrent = originalTotalPaid + currentSalaryAndAdvancePayments;
        
        // Оставшаяся зарплата = изначальная зарплата - общая выплаченная сумма
        var remainingSalary = originalSalary - totalPaidIncludingCurrent;
        
        // Обновляем отображение только если есть информация о зарплате
        if ($('#remaining-salary-info').is(':visible')) {
            $('#worker-remaining-salary').text(formatNumber(Math.max(0, remainingSalary)));
        }
    }

    function formatNumber(num) {
        return parseFloat(num || 0).toLocaleString('ru-RU');
    }

    // Функция для форматирования ввода чисел с разделителями тысяч
    function formatNumberInput(value) {
        // Если значение пустое или undefined
        if (!value || value === '') return '';
        
        // Убираем все символы кроме цифр
        var numericValue = value.toString().replace(/[^\d]/g, '');
        
        if (numericValue === '') return '';
        
        // Форматируем с разделителями тысяч
        return parseInt(numericValue).toLocaleString('ru-RU');
    }

    // Функция для получения чистого числового значения из отформатированного поля
    function getNumericValue(formattedValue) {
        if (!formattedValue || formattedValue === '') return 0;
        var numericString = formattedValue.toString().replace(/[^\d]/g, '');
        return parseInt(numericString || '0');
    }

    // Валидация ввода - разрешаем только цифры и пробелы
    $(document).on('keypress', 'input[inputmode="numeric"]', function(e) {
        var char = String.fromCharCode(e.which);
        if (!/[\d\s]/.test(char) && e.which !== 8 && e.which !== 0) {
            e.preventDefault();
        }
    });

    // Предотвращаем вставку недопустимых символов
    $(document).on('paste', 'input[inputmode="numeric"]', function(e) {
        var paste = (e.originalEvent.clipboardData || window.clipboardData).getData('text');
        if (!/^[\d\s]*$/.test(paste)) {
            e.preventDefault();
        }
    });

    // Флаг для предотвращения рекурсивных вызовов автораспределения
    var isAutoDistributing = false;

    // Функция для автоматического распределения суммы между методами оплаты
    function autoDistributeAmount(changedInput) {
        if (isAutoDistributing) return; // Предотвращаем рекурсивные вызовы
        
        var paymentRow = changedInput.closest('tr');
        var allAmountInputs = paymentRow.find('.amount-input');
        
        if (allAmountInputs.length <= 1) return; // Нет смысла распределять, если инпут один
        
        // Получаем общую сумму, которую нужно выплатить для данного типа
        var totalAmountForType = 0;
        var paymentTypeId = paymentRow.data('type');
        
        // Для зарплаты и аванса - берем оставшуюся сумму
        if (paymentTypeId == <?= WorkerFinances::TYPE_SALARY ?> || paymentTypeId == <?= WorkerFinances::TYPE_ADVANCE ?>) {
            var originalSalary = parseFloat($('#worker-salary').text().replace(/[^\d.-]/g, '')) || 0;
            var originalTotalPaid = parseFloat($('#worker-total-paid').text().replace(/[^\d.-]/g, '')) || 0;
            
            // Считаем текущие выплаты зарплаты/аванса (кроме текущей строки)
            var currentPayments = 0;
            $('.payment-amount:not(:disabled)').each(function() {
                if ($(this).closest('tr')[0] !== paymentRow[0]) {
                    var amount = getNumericValue($(this).val());
                    var rowTypeId = $(this).closest('tr').data('type');
                    if (rowTypeId == <?= WorkerFinances::TYPE_SALARY ?> || rowTypeId == <?= WorkerFinances::TYPE_ADVANCE ?>) {
                        currentPayments += amount;
                    }
                }
            });
            
            totalAmountForType = Math.max(0, originalSalary - originalTotalPaid - currentPayments);
        } else {
            // Для других типов выплат - используем сумму из всех методов оплаты в этой строке
            allAmountInputs.each(function() {
                totalAmountForType += getNumericValue($(this).val());
            });
        }
        
        // Получаем сумму из измененного инпута
        var changedAmount = getNumericValue(changedInput.val());
        
        // Устанавливаем флаг для предотвращения рекурсивных вызовов
        isAutoDistributing = true;
        
        // Если введенная сумма больше или равна общей сумме - очищаем остальные инпуты
        if (changedAmount >= totalAmountForType) {
            allAmountInputs.not(changedInput).each(function() {
                $(this).val('');
            });
        } else {
            // Распределяем остаток между другими активными инпутами
            var remainingAmount = totalAmountForType - changedAmount;
            var otherActiveInputs = allAmountInputs.not(changedInput).filter(function() {
                return !$(this).prop('disabled');
            });
            
            if (otherActiveInputs.length > 0) {
                var amountPerInput = Math.floor(remainingAmount / otherActiveInputs.length);
                var remainder = remainingAmount % otherActiveInputs.length;
                
                otherActiveInputs.each(function(index) {
                    var amount = amountPerInput + (index < remainder ? 1 : 0);
                    if (amount > 0) {
                        $(this).val(formatNumberInput(amount.toString()));
                    } else {
                        $(this).val('');
                    }
                });
            }
        }
        
        // Сбрасываем флаг
        isAutoDistributing = false;
        
        // Пересчитываем общую сумму вручную
        calculateTotal();
    }

    // Применяем форматирование ко всем полям ввода чисел
    $(document).on('input', 'input[inputmode="numeric"]', function() {
        var cursorPosition = this.selectionStart;
        var oldValue = $(this).val();
        var newValue = formatNumberInput(oldValue);
        
        $(this).val(newValue);
        
        // Восстанавливаем позицию курсора с учетом изменения длины
        var lengthDiff = newValue.length - oldValue.length;
        try {
            this.setSelectionRange(cursorPosition + lengthDiff, cursorPosition + lengthDiff);
        } catch (e) {
            // Fallback для случаев, когда setSelectionRange не поддерживается
            this.setSelectionRange(newValue.length, newValue.length);
        }
        
        // Если это поле суммы в методах оплаты - запускаем автораспределение (только если не в процессе автораспределения)
        if ($(this).hasClass('amount-input') && !isAutoDistributing) {
            autoDistributeAmount($(this));
        } else {
            // Пересчитываем общую сумму только если не в процессе автораспределения
            if (!isAutoDistributing) {
                calculateTotal();
            }
        }
    });

    // Преобразуем отформатированные значения обратно в числа перед отправкой формы
    $('form').on('submit', function() {
        $('input[inputmode="numeric"]').each(function() {
            var numericValue = getNumericValue($(this).val());
            $(this).val(numericValue);
        });
    });

    // Clear errors when form fields change
    $('input, select').on('change', function() {
        var fieldId = $(this).attr('id');
        if (fieldId) {
            $('#' + fieldId + '-error').text('');
        }
    });
});
</script>

<?php

namespace app\modules\backend\services\worker_payment;

use app\common\models\Cashbox;
use app\common\models\CashboxDetail;
use app\common\models\PaymentType;
use app\modules\backend\models\WorkerFinances;
use app\modules\backend\models\WorkerSalary;
use app\modules\backend\models\Worker;
use app\common\models\Tracking;
use Yii;
use yii\db\Exception;
use yii\db\Transaction;

class WorkerPaymentService
{
    /**
     * Получить данные для формы создания выплаты
     */
    public function getFormData(): array
    {
        $workers = Worker::find()
            ->select(['id', 'full_name'])
            ->where(['deleted_at' => null])
            ->orderBy('full_name')
            ->asArray()
            ->all();

        $paymentTypes = WorkerFinances::getTypes();

        $cashboxes = Cashbox::find()
            ->where(['deleted_at' => null])
            ->orderBy('title')
            ->all();

        return [
            'workers' => $workers,
            'paymentTypes' => $paymentTypes,
            'cashboxes' => $cashboxes,
            'paymentTypeOptions' => PaymentType::getTypeLabels()
        ];
    }

    /**
     * Создать выплату работнику
     */
    public function createPayment(array $data): array
    {
        $transaction = Yii::$app->db->beginTransaction();
        
        try {
            // Валидация данных
            $validationResult = $this->validatePaymentData($data);
            if (!$validationResult['success']) {
                return $validationResult;
            }

            // Получение данных
            $worker = Worker::findOne($data['worker_id']);
            $cashbox = Cashbox::findOne($data['cashbox_id']);
            $amount = (float)$data['amount'];
            $paymentTypes = $data['payment_types'] ?? [];
            $paymentMethod = $data['payment_method']; // 1 - наличные, 2 - карта
            $month = $data['month'];

            // Проверка баланса кассы
            if ($cashbox->balance < $amount) {
                return [
                    'success' => false,
                    'message' => 'Недостаточно средств в кассе'
                ];
            }

            // Создание записей о выплатах
            $createdPayments = [];
            $totalAmount = 0;

            foreach ($paymentTypes as $paymentType) {
                $paymentAmount = (float)$paymentType['amount'];
                if ($paymentAmount <= 0) continue;

                $workerFinance = new WorkerFinances();
                $workerFinance->worker_id = $worker->id;
                $workerFinance->month = $month;
                $workerFinance->type = $paymentType['type'];
                $workerFinance->amount = $paymentAmount;
                $workerFinance->description = $paymentType['description'] ?? '';
                $workerFinance->created_at = date('Y-m-d H:i:s');

                if (!$workerFinance->save()) {
                    throw new Exception('Ошибка при сохранении выплаты: ' . json_encode($workerFinance->errors));
                }

                // Создание записи в кассе
                $cashboxDetail = new CashboxDetail();
                $cashboxDetail->cashbox_id = $cashbox->id;
                $cashboxDetail->amount = $paymentAmount;
                $cashboxDetail->type = CashboxDetail::TYPE_OUT;
                $cashboxDetail->payment_type = $paymentMethod;
                $cashboxDetail->description = $this->generateCashboxDescription($worker, $paymentType['type'], $month);
                $cashboxDetail->created_at = date('Y-m-d H:i:s');

                if (!$cashboxDetail->save()) {
                    throw new Exception('Ошибка при сохранении в кассе: ' . json_encode($cashboxDetail->errors));
                }

                // Создание tracking записи
                $tracking = new Tracking();
                $tracking->process_id = $workerFinance->id;
                $tracking->progress_type = Tracking::PAY_FOR_WORKER;
                $tracking->created_at = date('Y-m-d H:i:s');

                if (!$tracking->save()) {
                    throw new Exception('Ошибка при создании tracking: ' . json_encode($tracking->errors));
                }

                $createdPayments[] = $workerFinance;
                $totalAmount += $paymentAmount;
            }

            // Обновление баланса кассы
            $cashbox->balance -= $totalAmount;
            if (!$cashbox->save()) {
                throw new Exception('Ошибка при обновлении баланса кассы');
            }

            $transaction->commit();

            return [
                'success' => true,
                'message' => 'Выплата успешно создана',
                'payments' => $createdPayments,
                'total_amount' => $totalAmount
            ];

        } catch (\Exception $e) {
            $transaction->rollBack();
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Валидация данных для создания выплаты
     */
    private function validatePaymentData(array $data): array
    {
        $errors = [];

        // Проверка обязательных полей
        if (empty($data['worker_id'])) {
            $errors['worker_id'] = 'Выберите сотрудника';
        }

        if (empty($data['cashbox_id'])) {
            $errors['cashbox_id'] = 'Выберите кассу';
        }

        if (empty($data['payment_method'])) {
            $errors['payment_method'] = 'Выберите способ оплаты';
        }

        if (empty($data['month'])) {
            $errors['month'] = 'Выберите месяц';
        }

        if (empty($data['payment_types']) || !is_array($data['payment_types'])) {
            $errors['payment_types'] = 'Выберите хотя бы один тип выплаты';
        }

        // Проверка сумм
        $totalAmount = 0;
        if (!empty($data['payment_types'])) {
            foreach ($data['payment_types'] as $paymentType) {
                if (!empty($paymentType['amount'])) {
                    $amount = (float)$paymentType['amount'];
                    if ($amount <= 0) {
                        $errors['payment_types'] = 'Сумма должна быть больше 0';
                        break;
                    }
                    $totalAmount += $amount;
                }
            }
        }

        if ($totalAmount <= 0) {
            $errors['amount'] = 'Общая сумма должна быть больше 0';
        }

        // Проверка существования работника
        if (!empty($data['worker_id'])) {
            $worker = Worker::findOne($data['worker_id']);
            if (!$worker) {
                $errors['worker_id'] = 'Сотрудник не найден';
            }
        }

        // Проверка существования кассы
        if (!empty($data['cashbox_id'])) {
            $cashbox = Cashbox::findOne($data['cashbox_id']);
            if (!$cashbox) {
                $errors['cashbox_id'] = 'Касса не найдена';
            }
        }

        return [
            'success' => empty($errors),
            'errors' => $errors,
            'message' => empty($errors) ? 'Валидация прошла успешно' : 'Ошибки валидации'
        ];
    }

    /**
     * Генерация описания для записи в кассе
     */
    private function generateCashboxDescription(Worker $worker, int $paymentType, string $month): string
    {
        $typeNames = [
            WorkerFinances::TYPE_SALARY => 'Зарплата',
            WorkerFinances::TYPE_ADVANCE => 'Аванс',
            WorkerFinances::TYPE_BONUS => 'Бонус',
            WorkerFinances::TYPE_DEBT => 'Долг',
            WorkerFinances::TYPE_ONE_TIME_PAYMENT => 'Разовая выплата',
            WorkerFinances::TYPE_DEBT_PAYMENT => 'Погашение долга',
            WorkerFinances::TYPE_VACATION_PAY => 'Отпускные'
        ];

        $typeName = $typeNames[$paymentType] ?? 'Выплата';
        $monthName = date('m.Y', strtotime($month . '-01'));

        return "{$typeName} для {$worker->full_name} за {$monthName}";
    }

    /**
     * Получить информацию о зарплате работника
     */
    public function getWorkerSalaryInfo(int $workerId): array
    {
        $worker = Worker::findOne($workerId);
        if (!$worker) {
            return [
                'success' => false,
                'message' => 'Сотрудник не найден'
            ];
        }

        $salary = WorkerSalary::find()
            ->where(['worker_id' => $workerId])
            ->andWhere(['end_date' => '9999-12-31'])
            ->andWhere(['deleted_at' => null])
            ->one();

        return [
            'success' => true,
            'worker' => $worker,
            'salary' => $salary ? $salary->amount : 0,
            'has_salary' => (bool)$salary
        ];
    }

    /**
     * Получить информацию о выплатах работника за месяц
     */
    public function getWorkerPaymentsForMonth(int $workerId, string $month): array
    {
        $payments = WorkerFinances::find()
            ->where(['worker_id' => $workerId])
            ->andWhere(['month' => $month])
            ->andWhere(['deleted_at' => null])
            ->all();

        $totalPaid = 0;
        $paymentsByType = [];

        foreach ($payments as $payment) {
            $totalPaid += $payment->amount;
            if (!isset($paymentsByType[$payment->type])) {
                $paymentsByType[$payment->type] = 0;
            }
            $paymentsByType[$payment->type] += $payment->amount;
        }

        return [
            'success' => true,
            'payments' => $payments,
            'total_paid' => $totalPaid,
            'payments_by_type' => $paymentsByType
        ];
    }
}

<!DOCTYPE html>
<html lang="ru">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест форматирования чисел (исправленная версия)</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }

        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
        }

        input[inputmode="numeric"] {
            padding: 8px;
            margin: 5px;
            width: 200px;
            text-align: right;
        }

        button {
            padding: 10px 15px;
            margin: 5px;
        }

        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #f0f0f0;
        }

        .demo-row {
            margin: 10px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
    </style>
</head>

<body>
    <h1>Тест форматирования чисел (исправленная версия)</h1>

    <div class="test-section">
        <h3>Форматирование при вводе</h3>
        <p>Введите число - оно будет отформатировано с разделителями тысяч:</p>
        <input type="text" inputmode="numeric" pattern="[0-9\s]*" placeholder="Введите число...">
        <div class="result" id="result-1">Начните вводить число...</div>
    </div>

    <div class="test-section">
        <h3>Автораспределение между двумя полями</h3>
        <p>Общая сумма: 1,000,000. Введите сумму в одно поле - остаток распределится во второе:</p>
        <div class="demo-row">
            <label>Наличные:</label>
            <input type="text" class="amount-input" inputmode="numeric" pattern="[0-9\s]*" placeholder="Наличные"
                data-total="1000000">
        </div>
        <div class="demo-row">
            <label>Карта:</label>
            <input type="text" class="amount-input" inputmode="numeric" pattern="[0-9\s]*" placeholder="Карта"
                data-total="1000000">
        </div>
        <div class="result" id="result-2">Распределение будет показано здесь...</div>
    </div>

    <div class="test-section">
        <h3>Тест отправки формы</h3>
        <form id="test-form">
            <div class="demo-row">
                <label>Сумма 1:</label>
                <input type="text" name="amount1" inputmode="numeric" pattern="[0-9\s]*" value="1 000 000">
            </div>
            <div class="demo-row">
                <label>Сумма 2:</label>
                <input type="text" name="amount2" inputmode="numeric" pattern="[0-9\s]*" value="2 500 000">
            </div>
            <button type="submit">Отправить (проверка преобразования)</button>
        </form>
        <div class="result" id="result-3">Нажмите "Отправить" для проверки...</div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // Функции из основного файла (скопированы)
        function formatNumberInput(value) {
            if (!value || value === '') return '';
            var numericValue = value.toString().replace(/[^\d]/g, '');
            if (numericValue === '') return '';
            return parseInt(numericValue).toLocaleString('ru-RU');
        }

        function getNumericValue(formattedValue) {
            if (!formattedValue || formattedValue === '') return 0;
            var numericString = formattedValue.toString().replace(/[^\d]/g, '');
            return parseInt(numericString || '0');
        }

        var isAutoDistributing = false;

        function autoDistributeAmount(changedInput) {
            if (isAutoDistributing) return;

            var total = parseInt(changedInput.attr('data-total')) || 0;
            var changedAmount = getNumericValue(changedInput.val());

            isAutoDistributing = true;

            var allInputs = $('.amount-input');
            var otherInputs = allInputs.not(changedInput);

            if (changedAmount >= total) {
                otherInputs.val('');
            } else {
                var remainingAmount = total - changedAmount;
                otherInputs.each(function () {
                    $(this).val(formatNumberInput(remainingAmount.toString()));
                });
            }

            isAutoDistributing = false;

            $('#result-2').html(`
                <strong>Распределение:</strong><br>
                Всего: ${formatNumberInput(total.toString())}<br>
                Введено: ${formatNumberInput(changedAmount.toString())}<br>
                Остаток: ${formatNumberInput((total - changedAmount).toString())}
            `);
        }

        // Валидация ввода
        $(document).on('keypress', 'input[inputmode="numeric"]', function (e) {
            var char = String.fromCharCode(e.which);
            if (!/[\d\s]/.test(char) && e.which !== 8 && e.which !== 0) {
                e.preventDefault();
            }
        });

        $(document).on('paste', 'input[inputmode="numeric"]', function (e) {
            var paste = (e.originalEvent.clipboardData || window.clipboardData).getData('text');
            if (!/^[\d\s]*$/.test(paste)) {
                e.preventDefault();
            }
        });

        // Форматирование
        $(document).on('input', 'input[inputmode="numeric"]', function () {
            var cursorPosition = this.selectionStart;
            var oldValue = $(this).val();
            var newValue = formatNumberInput(oldValue);

            $(this).val(newValue);

            var lengthDiff = newValue.length - oldValue.length;
            try {
                this.setSelectionRange(cursorPosition + lengthDiff, cursorPosition + lengthDiff);
            } catch (e) {
                this.setSelectionRange(newValue.length, newValue.length);
            }

            // Обновляем результат для первого теста
            $('#result-1').html(`
                <strong>Отформатированное значение:</strong> "${newValue}"<br>
                <strong>Числовое значение:</strong> ${getNumericValue(newValue)}
            `);

            if ($(this).hasClass('amount-input') && !isAutoDistributing) {
                autoDistributeAmount($(this));
            }
        });

        // Тест отправки формы
        $('#test-form').on('submit', function (e) {
            e.preventDefault();

            var formData = {};
            $(this).find('input[inputmode="numeric"]').each(function () {
                var name = $(this).attr('name');
                var displayValue = $(this).val();
                var numericValue = getNumericValue(displayValue);

                formData[name] = {
                    display: displayValue,
                    numeric: numericValue
                };

                $(this).val(numericValue);
            });

            $('#result-3').html(`
                <strong>Данные для отправки:</strong><br>
                ${Object.keys(formData).map(key =>
                `${key}: "${formData[key].display}" → ${formData[key].numeric}`
            ).join('<br>')}<br><br>
                <em>Значения изменены в полях на 2 секунды для демонстрации...</em>
            `);

            setTimeout(() => {
                $(this).find('input[inputmode="numeric"]').each(function () {
                    var name = $(this).attr('name');
                    $(this).val(formData[name].display);
                });
            }, 2000);
        });
    </script>
</body>

</html>
<!DOCTYPE html>
<html lang="ru">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест форматирования чисел</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }

        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
        }

        input[type="number"] {
            padding: 8px;
            margin: 5px;
            width: 200px;
        }

        button {
            padding: 10px 15px;
            margin: 5px;
        }

        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #f0f0f0;
        }
    </style>
</head>

<body>
    <h1>Тест форматирования чисел</h1>

    <div class="test-section">
        <h3>Форматирование при вводе</h3>
        <p>Введите число - оно будет отформатировано с разделителями тысяч:</p>
        <input type="number" id="test-input-1" placeholder="Введите число...">
        <div class="result" id="result-1"></div>
    </div>

    <div class="test-section">
        <h3>Автораспределение между двумя полями</h3>
        <p>Общая сумма: 1,000,000. Введите сумму в одно поле - остаток распределится во второе:</p>
        <input type="number" class="amount-input" data-total="1000000" placeholder="Поле 1">
        <input type="number" class="amount-input" data-total="1000000" placeholder="Поле 2">
        <div class="result" id="result-2"></div>
    </div>

    <div class="test-section">
        <h3>Тест отправки формы</h3>
        <form id="test-form">
            <input type="number" name="amount1" placeholder="Сумма 1" value="1000000">
            <input type="number" name="amount2" placeholder="Сумма 2" value="2500000">
            <button type="submit">Отправить</button>
        </form>
        <div class="result" id="result-3"></div>
    </div>

    <script>
        // Функции из основного файла
        function formatNumberInput(value) {
            var numericValue = value.replace(/[^\d]/g, '');
            if (numericValue === '') return '';
            return parseInt(numericValue).toLocaleString('ru-RU');
        }

        function getNumericValue(formattedValue) {
            return parseInt(formattedValue.replace(/[^\d]/g, '') || '0');
        }

        function autoDistributeAmount(changedInput) {
            var allInputs = $('.amount-input');
            var total = parseInt(changedInput.attr('data-total')) || 0;
            var changedAmount = getNumericValue(changedInput.val());

            if (changedAmount >= total) {
                allInputs.not(changedInput).val('');
                return;
            }

            var remainingAmount = total - changedAmount;
            var otherInputs = allInputs.not(changedInput);

            if (otherInputs.length > 0) {
                var amountPerInput = Math.floor(remainingAmount / otherInputs.length);
                var remainder = remainingAmount % otherInputs.length;

                otherInputs.each(function (index) {
                    var amount = amountPerInput + (index < remainder ? 1 : 0);
                    if (amount > 0) {
                        $(this).val(formatNumberInput(amount.toString()));
                    } else {
                        $(this).val('');
                    }
                });
            }
        }

        // Применяем форматирование ко всем полям
        $(document).on('input', 'input[type="number"]', function () {
            var cursorPosition = this.selectionStart;
            var oldValue = $(this).val();
            var newValue = formatNumberInput(oldValue);

            $(this).val(newValue);

            var lengthDiff = newValue.length - oldValue.length;
            this.setSelectionRange(cursorPosition + lengthDiff, cursorPosition + lengthDiff);

            if ($(this).hasClass('amount-input')) {
                autoDistributeAmount($(this));
            }

            // Обновляем результат для первого теста
            if ($(this).attr('id') === 'test-input-1') {
                $('#result-1').html(`
                    <strong>Отформатированное значение:</strong> ${newValue}<br>
                    <strong>Числовое значение:</strong> ${getNumericValue(newValue)}
                `);
            }
        });

        // Тест отправки формы
        $('#test-form').on('submit', function (e) {
            e.preventDefault();

            var formData = {};
            $(this).find('input[type="number"]').each(function () {
                var name = $(this).attr('name');
                var displayValue = $(this).val();
                var numericValue = getNumericValue(displayValue);

                formData[name] = {
                    display: displayValue,
                    numeric: numericValue
                };

                // Временно меняем значение для "отправки"
                $(this).val(numericValue);
            });

            $('#result-3').html(`
                <strong>Данные для отправки:</strong><br>
                ${Object.keys(formData).map(key =>
                `${key}: ${formData[key].display} → ${formData[key].numeric}`
            ).join('<br>')}
            `);

            // Возвращаем отформатированные значения
            setTimeout(() => {
                $(this).find('input[type="number"]').each(function () {
                    var name = $(this).attr('name');
                    $(this).val(formData[name].display);
                });
            }, 2000);
        });

        // jQuery stub для тестирования (упрощенная версия)
        if (typeof $ === 'undefined') {
            window.$ = function (selector) {
                if (selector.startsWith('#')) {
                    return {
                        val: function (value) {
                            var el = document.querySelector(selector);
                            if (value !== undefined) {
                                el.value = value;
                                return this;
                            }
                            return el.value;
                        },
                        html: function (html) {
                            document.querySelector(selector).innerHTML = html;
                        },
                        attr: function (name) {
                            return document.querySelector(selector).getAttribute(name);
                        }
                    };
                }
                return {
                    each: function () { },
                    not: function () { return this; },
                    val: function () { return ''; }
                };
            };
            $.fn = {};
        }
    </script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</body>

</html>